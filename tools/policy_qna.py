import logging
import json
from fastapi import HTTPException
from pydantic import BaseModel
import os

# Set up logger
logger = logging.getLogger(__name__)
# from azure.core.credentials import AzureKeyCredential
# from azure.search.documents import SearchClient
# from azure.search.documents.models import VectorizedQuery
import requests
import time
from dotenv import load_dotenv
import copy
from utils.helper_function import generate_embedding, create_opensearch_client
# from .audit import *
import traceback
from typing import Dict, Any
load_dotenv()

# Environment variables (replace with secure secret management for production use)
# AZURE_OPENAI_PREVIEW_API_VERSION = os.environ.get("AZURE-OPENAI-PREVIEW-API-VERSION")
# AZURE_OPENAI_ENDPOINT = os.environ.get("AZURE-OPENAI-ENDPOINT")
# AZURE_OPENAI_EMBEDDING_ENDPOINT = os.environ.get("AZURE-OPENAI-EMBEDDING-ENDPOINT")
# AZURE_OPENAI_EMBEDDING_NAME =os.environ.get("AZURE-OPENAI-EMBEDDING-NAME")
# AZURE_OPENAI_EMBEDDING_KEY = os.environ.get("AZURE-OPENAI-EMBEDDING-KEY")
# AZURE_OPENAI_KEY = os.environ.get("AZURE-OPENAI-KEY")
# AZURE_OPENAI_MODEL = os.environ.get("AZURE-OPENAI-MODEL")
# AZURE_SEARCH_SERVICE = os.environ.get("AZURE-SEARCH-SERVICE")
# AZURE_SEARCH_INDEX = os.environ.get("AZURE-SEARCH-INDEX")
# AZURE_SEARCH_KEY = os.environ.get("AZURE-SEARCH-KEY")
# AZURE_SEARCH_VECTOR_COLUMNS = os.environ.get("AZURE-SEARCH-VECTOR-COLUMNS")
# AZURE_SEARCH_SEMANTIC_SEARCH_CONFIG = os.environ.get("AZURE-SEARCH-SEMANTIC-SEARCH-CONFIG")
HRMS_API_URL = os.environ.get("HRMS-API-URL")
HRMS_TOKEN = os.environ.get("HRMS-TOKEN")
TENANT_ID = os.environ.get("TENANT-ID")
HRMS_LOGIN_API_URL = os.environ.get("HRMS-LOGIN-API-URL")
HRMS_LOGIN_PASSWORD = os.environ.get("HRMS-LOGIN-API-PASSWORD")
HRMS_LOGIN_MAIL = os.environ.get("HRMS-LOGIN-API-MAIL")
AUDIT_ENDPOINT = os.environ.get("AUDIT-ENDPOINT")
POLICY_INDEX_NAME = os.environ.get("POLICY-INDEX-NAME")


ORG_ENTITY= {
    "SG-MT-003": "MOURI Tech Pte. Ltd.",
    "US-MT-002": "MOURI Tech LLC",
    "IN-MT-001": "MOURI Tech Ltd.",
    "US-VS-005": "Vertisystem Inc",
    "IN-VS-012": "Vertisystem Global Pvt. Ltd.",
    "US-TG-007": "TekGigz LLC",
    "US-VT-006": "V3Tech Solutions Inc"
}

class CitationManager:
    _instance = None
    citations = []

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(CitationManager, cls).__new__(cls)
        return cls._instance

    def add_citation(self, citation):
        self.citations.append(citation)

    def get_citations(self):
        return self.citations
    
    def clear_citations(self):
        self.citations=[]

def validate_parameters(req_body):
    required_params = ['question', 'userid', 'country','legal_entity']
    missing_params = [param for param in required_params if not req_body.get(param)]
    
    if missing_params:
        # Create a detailed error message
        missing_message = f"Missing parameters: {', '.join(missing_params)}"
        raise ValueError(missing_message)
    
    # Return the required parameters if all are present
    return (
        req_body['question'],
        req_body['userid'],
        req_body['country'],
        req_body['legal_entity']
    )




# def regenerate_token(legal_entity_code):
#     global HRMS_TOKEN
#     try:
#         # Prepare login payload as per the API specs
#         payload = {
#             "emailId": HRMS_LOGIN_MAIL,
#             "password": HRMS_LOGIN_PASSWORD,
#             "type": "authenticator_identifier"
#         }

#         # Make the request to the login endpoint to get a new token
#         login_url = HRMS_LOGIN_API_URL
#         headers = {
#             "accept": "*/*",
#             "x-tenantId": TENANT_ID,
#             "x-entityId": legal_entity_code,
#             "Content-Type": "application/json"
#         }
        
#         # Sending POST request for token
#         login_response = requests.post(login_url, headers=headers, json=payload)

#         # Check the login response status
#         if login_response.status_code == 200:
#             # Parsing the token from the response
#             token_data = login_response.json()
#             HRMS_TOKEN = token_data.get("token")  # Fetching the 'token' from the response body
#         else:
#             raise HTTPException(status_code=500, detail="Token regeneration failed.")

#     except Exception as e:
#         raise HTTPException(status_code=500, detail="Failed to regenerate token.")


def get_vectorized_query_resp(
    client,
    embedding: list,
    index_name: str,
    k: int = 1,
    filter_clauses: list = None  # Renamed for clarity
) -> list:
    query_body = {
        "size": k,
        "query": {
            "bool": {
                "should": [
                    {
                        "knn": {
                            "embedding": {
                                "vector": embedding,
                                "k": k
                            }
                        }
                    }
                ],
                "filter": filter_clauses if filter_clauses else []
            }
        }
    }
    # print(query_body)
    response = client.search(index=index_name, body=query_body)
    return response["hits"]["hits"]



# Policy information retrieval
def get_policy_information(question: str, usercountry: str = "IN", userlegalentity: str = "IN-MT-001") -> Dict[str, Any]:
    """
    Retrieves policy-related information using OpenSearch.

    Args:
        question: The policy-related question to search for
        usercountry: The country code (default: "IN")
        userlegalentity: The legal entity code (default: "IN-MT-001")

    Returns:
        Dictionary containing policy information
    """
    try:

        logger.info(f"Processing policy question: {question}")
        logger.info(f"userlegalentity: {userlegalentity}")
        logger.info(f"usercountry: {usercountry}")
        logger.info("====== get_policy_information ====== ")
       # logger.info("legal_entity: ", userlegalentity)
        logger.info("ORG_ENTITY: ", ORG_ENTITY)
        company_name = ORG_ENTITY.get(userlegalentity)
        logger.info(f"Company name: {company_name}")
        start_embedding_time = time.time()
        embedding_vector = generate_embedding(question, os.environ['GOOGLE_EMBEDDING_MODEL'])
        embedding_elapsed_time = time.time() - start_embedding_time
        logger.info(f"Time taken to generate embedding: {embedding_elapsed_time:.2f} seconds")

        logger.info("Embedding generation completed")

        logger.info("Creating opensearch client")

        search_client = create_opensearch_client()

        # Step 1: Create filter clauses dynamically
        filter_clauses = []

        if userlegalentity:
            filter_clauses.append({
                "terms": {
                    "legal_entity.keyword": [userlegalentity.upper()]
                }
            })

        if usercountry and usercountry.lower() != "global":
            filter_clauses.append({
                "terms": {
                    "country.keyword": ["IN"]
                }
            })

        logging.info(f"filter: {filter_clauses}")
        logging.info('============================')

        # Step 2: Perform vector + filter query
        start_time = time.time()
        results = get_vectorized_query_resp(
            client=search_client,
            embedding=embedding_vector,
            index_name=POLICY_INDEX_NAME,
            k=1,
            filter_clauses=filter_clauses
        )
        elapsed_time = time.time() - start_time
        logger.info(f"Time taken to get vectorized query response: {elapsed_time:.2f} seconds")

        logger.info(f"Vectorized query response received: {results}")

        final_results = []
        logger.info(f"Processing {len(results)} opensearch results")
        for result in results:
            formatted_result = {
                "policy_id": result['_source'].get("_id", ""),
                "title": result['_source'].get("title", ""),
                "category": result['_source'].get("category", ""),
                "subcategory": result['_source'].get("subcategory", ""),
                "content": result['_source'].get("text", ""),
                "relevance_score": result['_source'].get("_score", 0),
                "effective_date": result['_source'].get("effective_date", ""),
                "last_updated": result['_source'].get("last_updated", ""),
                "department": result['_source'].get("department", ""),
                "link": result['_source'].get("filepath", ""),
                "country": result['_source'].get("country", ""),
                "legal_entity": result['_source'].get("legal_entity", "")
            }
            final_results.append(formatted_result)
        
        logger.info(f"Final results: {final_results}")
        
        return {
            "status": "success",
            "data": final_results,
            "message": "Policy information retrieved successfully"
        }

    except Exception as e:
        logging.error(f"Error during policy info retrieval: {e}")
        return {
            "status": "error",
            "data": None,
            "message": f"An error occurred while retrieving the policy information: {str(e)}"
        }




        # Collect content from SourceStr1, which now includes the link
        # Collect content and link from each result
        retrieved_documents = []
        citation_manager=CitationManager()
        citation_count=1
        for result in results:
            logging.info(result)
            content = result.get("content", "")
            link = result.get("link", "")
            document_text = f"{content}\nLink: {link}" if link else content
            retrieved_documents.append(document_text)
            citation={
                "content" :  result.get("content", ""),
                "filename" : f"citation {citation_count}"

            }
            citation_manager.add_citation(citation)
            citation_count+=1
        logging.info(retrieved_documents)
        # Combine retrieved documents
        context = "\n\n---\n\n".join(retrieved_documents)
        #logging.info(retrieved_documents)

        # Step 4: Generate answer using Azure OpenAI based on retrieved documents
        # client = AzureOpenAI(
        #     api_key=AZURE_OPENAI_KEY,
        #     api_version=AZURE_OPENAI_PREVIEW_API_VERSION,
        #     azure_endpoint=AZURE_OPENAI_ENDPOINT
        # )
        logging.info("context")
        logging.info(context)
        prompt_instruction = f"""You are an AI agent that helps employees by answering questions. Here is the question asked by the user: "{question}". 
        Go through the top two relevant documents below to provide the answer. Include links if they are available. But if the link is not available, don't reference it.
        Also, if you gather the documents and there is nothing found that related closely to the question, do not give it as output and do not make any assumptions to answer.
        If the question doesn't closely relate to any policy, keep your answer short and concise, do not make any reccomendations or assumptions.
        If the answer is simple, you do not need to overly elaborate, the user can ask further questions if they need more detalis, it is not your job to provide them.
        The name of the company is - {company_name}
        Always try to include all the important or relevant points mentioned in the document in your response
        Do not mention any information regarding your process. 
        Do not include file names, document titles, or metadata in your response. Present the information as if directly answering the user's query
        Make sure all your responses are kind and friendly..

        Documents: {context}"""

       

        messages = [{"role": "user", "content": prompt_instruction}]
        
        # response = client.chat.completions.create(
        #     model=AZURE_OPENAI_MODEL,
        #     messages=messages,
        #     temperature=0.0,
        #     max_tokens=int(200)
        # )

        # Return the assistant's response
        return {"answer": results}

    except Exception as e:
        # Debug: Log exception details
        print(f"Exception in qna: {e}")
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))