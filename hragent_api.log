2025-07-24 10:26:14,210 - itsm_solution - INFO - Logging initialized
2025-07-24 10:26:14,210 - main - INFO - Initializing HRAgent Application with app_name=hragent_api
2025-07-24 10:26:14,210 - agents.hr_service_desk_agent - INFO - Creating HR Service Desk Agent with model gemini-2.0-flash
2025-07-24 10:26:14,210 - agents.hr_service_desk_agent - INFO - HR Service Desk Agent created successfully
2025-07-24 10:26:14,210 - agents.leave_management_agent - INFO - Creating Leave Management Agent with model gemini-2.0-flash
2025-07-24 10:26:14,210 - agents.leave_management_agent - INFO - Leave Management Agent created successfully
2025-07-24 10:26:14,210 - agents.attendance_management_agent - INFO - Creating Attendance Management Agent with model gemini-2.0-flash
2025-07-24 10:26:14,210 - agents.attendance_management_agent - INFO - Attendance Management Agent created successfully
2025-07-24 10:26:14,210 - agents.policy_agent - INFO - Creating Policy Agent with model gemini-2.0-flash
2025-07-24 10:26:14,210 - agents.policy_agent - INFO - Policy Agent created successfully
2025-07-24 10:26:14,210 - profile_agent - <PERSON>FO - Creating Profile Agent with model gemini-2.0-flash
2025-07-24 10:26:14,210 - profile_agent - INFO - Profile tools configured: get_employee_info, get_employee_work_profile
2025-07-24 10:26:14,210 - profile_agent - INFO - Enabling safety callbacks
2025-07-24 10:26:14,210 - profile_agent - INFO - Adding post-processing callback
2025-07-24 10:26:14,210 - profile_agent - INFO - Initializing Profile Agent with configuration
2025-07-24 10:26:14,210 - profile_agent - INFO - Profile Agent created successfully
2025-07-24 10:26:14,210 - agents.root_agent - INFO - Setting up audit trail for all agents
2025-07-24 10:26:14,210 - utils.audit_setup - INFO - Setting up audit trail for agent hr_root_agent
2025-07-24 10:26:14,217 - services.audit_service - INFO - Agent hr_root_agent already registered with ID 1
2025-07-24 10:26:14,217 - utils.audit_callbacks - INFO - Agent hr_root_agent registered with ID 1
2025-07-24 10:26:14,217 - utils.audit_setup - INFO - Registering system prompt for agent hr_root_agent
2025-07-24 10:26:14,218 - services.audit_service - INFO - Updated existing prompt for agent 1 with ID 1
2025-07-24 10:26:14,218 - utils.audit_setup - INFO - System prompt registered with ID 1
2025-07-24 10:26:14,218 - utils.callbacks - INFO - Agent hr_root_agent registered for audit callbacks
2025-07-24 10:26:14,218 - utils.audit_setup - INFO - Registered agent hr_root_agent for audit callbacks
2025-07-24 10:26:14,218 - utils.audit_setup - INFO - Audit trail set up for agent hr_root_agent
2025-07-24 10:26:14,218 - utils.audit_setup - INFO - Setting up audit trail for agent hr_service_desk_agent
2025-07-24 10:26:14,218 - services.audit_service - INFO - Agent hr_service_desk_agent already registered with ID 2
2025-07-24 10:26:14,218 - utils.audit_callbacks - INFO - Agent hr_service_desk_agent registered with ID 2
2025-07-24 10:26:14,218 - utils.audit_setup - INFO - Registering system prompt for agent hr_service_desk_agent
2025-07-24 10:26:14,219 - services.audit_service - INFO - Updated existing prompt for agent 2 with ID 2
2025-07-24 10:26:14,219 - utils.audit_setup - INFO - System prompt registered with ID 2
2025-07-24 10:26:14,219 - utils.callbacks - INFO - Agent hr_service_desk_agent registered for audit callbacks
2025-07-24 10:26:14,219 - utils.audit_setup - INFO - Registered agent hr_service_desk_agent for audit callbacks
2025-07-24 10:26:14,219 - utils.audit_setup - INFO - Audit trail set up for agent hr_service_desk_agent
2025-07-24 10:26:14,219 - utils.audit_setup - INFO - Setting up audit trail for agent leave_management_agent
2025-07-24 10:26:14,219 - services.audit_service - INFO - Agent leave_management_agent already registered with ID 3
2025-07-24 10:26:14,219 - utils.audit_callbacks - INFO - Agent leave_management_agent registered with ID 3
2025-07-24 10:26:14,219 - utils.audit_setup - INFO - Registering system prompt for agent leave_management_agent
2025-07-24 10:26:14,221 - services.audit_service - INFO - Updated existing prompt for agent 3 with ID 3
2025-07-24 10:26:14,221 - utils.audit_setup - INFO - System prompt registered with ID 3
2025-07-24 10:26:14,221 - utils.callbacks - INFO - Agent leave_management_agent registered for audit callbacks
2025-07-24 10:26:14,221 - utils.audit_setup - INFO - Registered agent leave_management_agent for audit callbacks
2025-07-24 10:26:14,221 - utils.audit_setup - INFO - Audit trail set up for agent leave_management_agent
2025-07-24 10:26:14,221 - utils.audit_setup - INFO - Setting up audit trail for agent attendance_management_agent
2025-07-24 10:26:14,221 - services.audit_service - INFO - Agent attendance_management_agent already registered with ID 4
2025-07-24 10:26:14,221 - utils.audit_callbacks - INFO - Agent attendance_management_agent registered with ID 4
2025-07-24 10:26:14,221 - utils.audit_setup - INFO - Registering system prompt for agent attendance_management_agent
2025-07-24 10:26:14,222 - services.audit_service - INFO - Updated existing prompt for agent 4 with ID 4
2025-07-24 10:26:14,222 - utils.audit_setup - INFO - System prompt registered with ID 4
2025-07-24 10:26:14,222 - utils.callbacks - INFO - Agent attendance_management_agent registered for audit callbacks
2025-07-24 10:26:14,222 - utils.audit_setup - INFO - Registered agent attendance_management_agent for audit callbacks
2025-07-24 10:26:14,222 - utils.audit_setup - INFO - Audit trail set up for agent attendance_management_agent
2025-07-24 10:26:14,222 - utils.audit_setup - INFO - Setting up audit trail for agent policy_agent
2025-07-24 10:26:14,222 - services.audit_service - INFO - Agent policy_agent already registered with ID 5
2025-07-24 10:26:14,222 - utils.audit_callbacks - INFO - Agent policy_agent registered with ID 5
2025-07-24 10:26:14,222 - utils.audit_setup - INFO - Registering system prompt for agent policy_agent
2025-07-24 10:26:14,222 - services.audit_service - INFO - Updated existing prompt for agent 5 with ID 5
2025-07-24 10:26:14,222 - utils.audit_setup - INFO - System prompt registered with ID 5
2025-07-24 10:26:14,222 - utils.callbacks - INFO - Agent policy_agent registered for audit callbacks
2025-07-24 10:26:14,222 - utils.audit_setup - INFO - Registered agent policy_agent for audit callbacks
2025-07-24 10:26:14,222 - utils.audit_setup - INFO - Audit trail set up for agent policy_agent
2025-07-24 10:26:14,222 - utils.audit_setup - INFO - Setting up audit trail for agent profile_agent
2025-07-24 10:26:14,223 - services.audit_service - INFO - Agent profile_agent already registered with ID 6
2025-07-24 10:26:14,223 - utils.audit_callbacks - INFO - Agent profile_agent registered with ID 6
2025-07-24 10:26:14,223 - utils.audit_setup - INFO - Registering system prompt for agent profile_agent
2025-07-24 10:26:14,223 - services.audit_service - INFO - Updated existing prompt for agent 6 with ID 6
2025-07-24 10:26:14,223 - utils.audit_setup - INFO - System prompt registered with ID 6
2025-07-24 10:26:14,223 - utils.callbacks - INFO - Agent profile_agent registered for audit callbacks
2025-07-24 10:26:14,223 - utils.audit_setup - INFO - Registered agent profile_agent for audit callbacks
2025-07-24 10:26:14,223 - utils.audit_setup - INFO - Audit trail set up for agent profile_agent
2025-07-24 10:26:14,223 - agents.root_agent - INFO - Audit trail setup complete
2025-07-24 10:26:14,223 - main - INFO - Using Enhanced In-Memory Session Service with ADK standards
2025-07-24 10:26:14,223 - services.session_service - INFO - Enhanced session service initialized following ADK standards
2025-07-24 10:26:14,223 - main - INFO - Using Enhanced In-Memory Memory Service for cross-session knowledge
2025-07-24 10:26:14,223 - services.memory_service - INFO - Enhanced memory service initialized following ADK standards
2025-07-24 10:26:14,223 - runners.runner_service - INFO - HR AI Assistant Runner Service initialized with app_name=hragent_api, session_service=EnhancedInMemorySessionService
2025-07-24 10:26:14,249 - itsm_solution - INFO - Logging initialized
2025-07-24 10:26:14,249 - main - INFO - Initializing HRAgent Application with app_name=hragent_api
2025-07-24 10:26:14,249 - agents.hr_service_desk_agent - INFO - Creating HR Service Desk Agent with model gemini-2.0-flash
2025-07-24 10:26:14,249 - agents.hr_service_desk_agent - INFO - HR Service Desk Agent created successfully
2025-07-24 10:26:14,249 - agents.leave_management_agent - INFO - Creating Leave Management Agent with model gemini-2.0-flash
2025-07-24 10:26:14,249 - agents.leave_management_agent - INFO - Leave Management Agent created successfully
2025-07-24 10:26:14,249 - agents.attendance_management_agent - INFO - Creating Attendance Management Agent with model gemini-2.0-flash
2025-07-24 10:26:14,249 - agents.attendance_management_agent - INFO - Attendance Management Agent created successfully
2025-07-24 10:26:14,249 - agents.policy_agent - INFO - Creating Policy Agent with model gemini-2.0-flash
2025-07-24 10:26:14,249 - agents.policy_agent - INFO - Policy Agent created successfully
2025-07-24 10:26:14,249 - profile_agent - INFO - Creating Profile Agent with model gemini-2.0-flash
2025-07-24 10:26:14,249 - profile_agent - INFO - Profile tools configured: get_employee_info, get_employee_work_profile
2025-07-24 10:26:14,249 - profile_agent - INFO - Enabling safety callbacks
2025-07-24 10:26:14,249 - profile_agent - INFO - Adding post-processing callback
2025-07-24 10:26:14,249 - profile_agent - INFO - Initializing Profile Agent with configuration
2025-07-24 10:26:14,249 - profile_agent - INFO - Profile Agent created successfully
2025-07-24 10:26:14,249 - agents.root_agent - INFO - Setting up audit trail for all agents
2025-07-24 10:26:14,249 - utils.audit_setup - INFO - Setting up audit trail for agent hr_root_agent
2025-07-24 10:26:14,249 - utils.audit_setup - INFO - Registering system prompt for agent hr_root_agent
2025-07-24 10:26:14,250 - services.audit_service - INFO - Updated existing prompt for agent 1 with ID 1
2025-07-24 10:26:14,250 - utils.audit_setup - INFO - System prompt registered with ID 1
2025-07-24 10:26:14,250 - utils.callbacks - INFO - Agent hr_root_agent registered for audit callbacks
2025-07-24 10:26:14,250 - utils.audit_setup - INFO - Registered agent hr_root_agent for audit callbacks
2025-07-24 10:26:14,250 - utils.audit_setup - INFO - Audit trail set up for agent hr_root_agent
2025-07-24 10:26:14,250 - utils.audit_setup - INFO - Setting up audit trail for agent hr_service_desk_agent
2025-07-24 10:26:14,250 - utils.audit_setup - INFO - Registering system prompt for agent hr_service_desk_agent
2025-07-24 10:26:14,251 - services.audit_service - INFO - Updated existing prompt for agent 2 with ID 2
2025-07-24 10:26:14,251 - utils.audit_setup - INFO - System prompt registered with ID 2
2025-07-24 10:26:14,251 - utils.callbacks - INFO - Agent hr_service_desk_agent registered for audit callbacks
2025-07-24 10:26:14,251 - utils.audit_setup - INFO - Registered agent hr_service_desk_agent for audit callbacks
2025-07-24 10:26:14,251 - utils.audit_setup - INFO - Audit trail set up for agent hr_service_desk_agent
2025-07-24 10:26:14,251 - utils.audit_setup - INFO - Setting up audit trail for agent leave_management_agent
2025-07-24 10:26:14,251 - utils.audit_setup - INFO - Registering system prompt for agent leave_management_agent
2025-07-24 10:26:14,251 - services.audit_service - INFO - Updated existing prompt for agent 3 with ID 3
2025-07-24 10:26:14,251 - utils.audit_setup - INFO - System prompt registered with ID 3
2025-07-24 10:26:14,251 - utils.callbacks - INFO - Agent leave_management_agent registered for audit callbacks
2025-07-24 10:26:14,251 - utils.audit_setup - INFO - Registered agent leave_management_agent for audit callbacks
2025-07-24 10:26:14,251 - utils.audit_setup - INFO - Audit trail set up for agent leave_management_agent
2025-07-24 10:26:14,251 - utils.audit_setup - INFO - Setting up audit trail for agent attendance_management_agent
2025-07-24 10:26:14,251 - utils.audit_setup - INFO - Registering system prompt for agent attendance_management_agent
2025-07-24 10:26:14,252 - services.audit_service - INFO - Updated existing prompt for agent 4 with ID 4
2025-07-24 10:26:14,252 - utils.audit_setup - INFO - System prompt registered with ID 4
2025-07-24 10:26:14,252 - utils.callbacks - INFO - Agent attendance_management_agent registered for audit callbacks
2025-07-24 10:26:14,252 - utils.audit_setup - INFO - Registered agent attendance_management_agent for audit callbacks
2025-07-24 10:26:14,252 - utils.audit_setup - INFO - Audit trail set up for agent attendance_management_agent
2025-07-24 10:26:14,252 - utils.audit_setup - INFO - Setting up audit trail for agent policy_agent
2025-07-24 10:26:14,252 - utils.audit_setup - INFO - Registering system prompt for agent policy_agent
2025-07-24 10:26:14,252 - services.audit_service - INFO - Updated existing prompt for agent 5 with ID 5
2025-07-24 10:26:14,252 - utils.audit_setup - INFO - System prompt registered with ID 5
2025-07-24 10:26:14,252 - utils.callbacks - INFO - Agent policy_agent registered for audit callbacks
2025-07-24 10:26:14,252 - utils.audit_setup - INFO - Registered agent policy_agent for audit callbacks
2025-07-24 10:26:14,252 - utils.audit_setup - INFO - Audit trail set up for agent policy_agent
2025-07-24 10:26:14,252 - utils.audit_setup - INFO - Setting up audit trail for agent profile_agent
2025-07-24 10:26:14,252 - utils.audit_setup - INFO - Registering system prompt for agent profile_agent
2025-07-24 10:26:14,253 - services.audit_service - INFO - Updated existing prompt for agent 6 with ID 6
2025-07-24 10:26:14,253 - utils.audit_setup - INFO - System prompt registered with ID 6
2025-07-24 10:26:14,253 - utils.callbacks - INFO - Agent profile_agent registered for audit callbacks
2025-07-24 10:26:14,253 - utils.audit_setup - INFO - Registered agent profile_agent for audit callbacks
2025-07-24 10:26:14,253 - utils.audit_setup - INFO - Audit trail set up for agent profile_agent
2025-07-24 10:26:14,253 - agents.root_agent - INFO - Audit trail setup complete
2025-07-24 10:26:14,253 - main - INFO - Using Enhanced In-Memory Session Service with ADK standards
2025-07-24 10:26:14,253 - services.session_service - INFO - Enhanced session service initialized following ADK standards
2025-07-24 10:26:14,253 - main - INFO - Using Enhanced In-Memory Memory Service for cross-session knowledge
2025-07-24 10:26:14,253 - services.memory_service - INFO - Enhanced memory service initialized following ADK standards
2025-07-24 10:26:14,253 - runners.runner_service - INFO - HR AI Assistant Runner Service initialized with app_name=hragent_api, session_service=EnhancedInMemorySessionService
